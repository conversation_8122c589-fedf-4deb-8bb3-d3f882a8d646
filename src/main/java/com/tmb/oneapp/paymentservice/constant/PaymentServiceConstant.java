package com.tmb.oneapp.paymentservice.constant;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class PaymentServiceConstant {

    /* HEADER CONSTANTS */
    public static final String HEADER_CORRELATION_ID = "X-Correlation-ID";
    public static final String X_CRM_ID = "x-crmid";
    public static final String HEADER_OS_VERSION = "os-version";
    public static final String HEADER_APP_VERSION = "App-Version";
    public static final String HEADER_ACCEPT_LANGUAGE = "Accept-Language";
    public static final String HEADER_CRM_ID = "X-crmId";
    public static final String HEADER_TRANSACTION_ID = "X-Transaction-ID";
    public static final String REQUEST_DATE_TIME = "request-datetime";
    public static final String DD_CONSENT_REGISTRATION_SERVICE_NAME = "dd-consent-registration-apply-request";
    public static final String CONTENT_TYPE = "Content-Type";
    public static final String CONTENT_TYPE_VALUE = "application/json";
    public static final String X_CRMID = "x-crmid";
    public static final String CONTENT_TYPE_VALUE_WITH_UTF8 = "application/json;charset=UTF-8";
    public static final String HEADER_CHANNEL = "channel";
    public static final String HEADER_API_KEY = "x-api-key";
    public static final String HEADER_X_PARTNER_NAME = "x-partner-name";

    /* VALUE CONSTANTS */
    public static final String BILLPAY_CONFIGURATION_MODULE = "billpay_module";
    public static final String CACHE_FETCHING_ERROR_MESSAGE = "exception while fetching data from Redis {}";
    public static final String HEADER_REQUEST_UUID = "request-uid";
    public static final String HEADER_APP_ID = "request-app-id";
    public static final String CHANNEL_MB = "mb";
    public static final String HEADER_SERVICE_NAME = "service-name";
    public static final String HEADER_REQUEST_DATE_TIME = "request-datetime";
    public static final String VALID_CHANNEL_TOPUP_CHANNEL_CODE = "02";
    public static final String BILLER_REQUIRE_AMOUNT = "BILLER.RequireAmount";
    public static final String BILLER_START_TIME = "BILLER.StartTime";
    public static final String BILLER_END_TIME = "BILLER.EndTime";
    public static final String BILLER_FULL_PAYMENT = "BILLER.FullPayment";
    public static final String BILLER_SERVICE_TYPE = "BILLER.ServiceType";
    public static final String BILLER_TRANSACTION_TYPE = "BILLER.TransactionType";
    public static final String BILLER_ALLOW_SET_SCHEDULE = "BILLER.AllowSetSchedule";
    public static final String BILLER_ESUR_ENCRYPTED_MERCHANT_KEY = "ESUR.EncryptKey";
    public static final String BILLER_ESUR_FGURL = "ESUR.FGURL";
    public static final Integer DEFAULT_MAX_LENGTH_REF1 = 20;
    public static final String BILLER_IS_MOBILE = "BILLER.Ref1AsMobileNo";
    public static final String BILLER_REG_EX = "BILLER.Ref1RegEx";
    public static final String BILLER_ALLOW_REF_1_ALPHA_NUM = "BILLER.AllowRef1AlphaNum";
    public static final String KEYBOARD_LAYOUT_ALPHANUM = "ALPHNUM";
    public static final String KEYBOARD_LAYOUT_NUMERIC = "NUMERIC";
    public static final String BILLER_REF2_REG_EX = "BILLER.Ref2RegEx";
    public static final String BILLER_REF2_KEYBOARD_ONLY_NUMERIC = "BILLER.AllowRef2OnlyNumeric";
    public static final String BILLER_URL_MISC_DATA_NAME = "BILLER.BillerURL";
    public static final String BILLER_EPAYMENT_FGURL = "EPY.FGURL";
    public static final String BILLER_EPAYMENT_BGURL = "EPY.BGURL";
    public static final String BILLER_EPAYMENT_ENCRYPTED_MERCHANT_KEY = "EPY.EncryptKey";
    public static final String VALID_CHANNEL_EPAYMENT_CHANNEL_CODE = "53";
    public static final Integer SIX_CATEGORY_ID_BILL_PAY = 6;
    public static final String BILLER_COMP_CODE_AEON = "0803";
    public static final String BILLER_COMP_CODE_KTC = "0967";
    public static final String BILLER_COMP_CODE_CITIBANK = "0072";
    public static final int COMP_CODE_PROMPT_LENGTH = 15;
    public static final String THREE_BILLER_METHOD_ID_BILL_PAY = "3";
    public static final String BILLER_COMP_CODE_E_WALLET = "EW01";
    public static final String BILLER_REQUIRE_KEY_IN = "BILLER.RequireKeyIn";
    public static final String BILLER_OLN_AMOUNT_MAX = "OLN.AMOUNT.MAX";
    public static final String BILLER_OLN_AMOUNT_MIN = "OLN.AMOUNT.MIN";
    public static final String BILL_PAY_MODULE_ID = "billpay_module";
    public static final String BILLER_COMP_CODE_TRUE_MOVE_H = "2135";
    public static final String COMMA_DELIMITER = ",";
    public static final String COMMON_CONFIG_CACHE_SUFFIX = "_config";
    public static final String PAYMENT_SERVICE = "payment-service";
    public static final String SERVICE_NAME = "service-name";
    public static final String SUCCESS_CODE = "0000";
    public static final String SUCCESS_MESSAGE = "success";
    public static final String FAILED_CODE = "0001";
    public static final String FAILED_MESSAGE = "failed";
    public static final String DB_FAILED_CODE = "0100";
    public static final String DB_FAILED_DESCRIPTION = "Cannot do this transaction";
    public static final String BILLER_GROUP_TYPE_BILL_PAY = "0";
    public static final String BILLER_GROUP_TYPE_TOP_UP = "1";
    public static final String MOBILE_CHANNEL_CODE = "02";
    public static final String INTERNET_CHANNEL_CODE = "01";
    public static final String CACHE_POSTING_ERROR_MESSAGE = "exception while setting data to Redis {}";
    public static final String BILLER_PREFIX = "biller_";
    public static final String BANK_TMB_VALIDATE_DATEFORMAT = "yyyy-MM-dd'T'HH:mm:ss";
    public static final String BILL_PAYMENT_CONFIRM_SERVICE_NAME = "bill-payment-confirm";
    public static final String BILL_PAYMENT_VERIFY_SERVICE_NAME = "bill-payment-verify";
    public static final String BILL_PAYMENT_AUTO_LOAN_VERIFY_SERVICE_NAME = "auto-loan-payment-verify";
    public static final String BILL_PAYMENT_AUTO_LOAN_CONFIRM_SERVICE_NAME = "auto-loan-payment-confirm";
    public static final String BILL_PAYMENT_CREDIT_CARD_CONFIRM_SERVICE_NAME = "credit-card-payment-confirm";

    public static final String DIRECT_DEBIT_REQUEST_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss";
    public static final String DIRECT_DEBIT_REQUEST_DATETIME_SUFFIX = "+07:00";
    public static final String REQUEST_APP_ID_VALUE = "A0478-MB";
    public static final String DASH = "-";
    public static final String TRANS_ID_DATE_FORMAT = "yyyyMMddHHmmss";
    public static final String DEFAULT_ACCOUNT_NO = "**********";

    public static final String TRANSACTION_STATUS_SUCCESS = "success";
    public static final String TRANSACTION_STATUS_FAILURE = "failure";
    public static final String BILL_COMP_CODE_1_2_CALL = "0137";
    public static final String BILL_COMP_CODE_AIS_FIBER = "2218";
    public static final String BILL_COMP_CODE_TRUE_BILL = "0328";
    public static final String BILL_COMP_CODE_TRUE_MOVE_H = "2135";
    public static final String BILL_COMP_CODE_TRUE_ONLINE = "2696";
    public static final String BILL_COMP_CODE_TRUE_VISION = "2267";
    public static final String BILL_COMP_AIS_TMB_EXCEPTION_PREFIX = "AIS_";

    public static final String PROMPT_PAY_VALIDATION_GET = "promptpay-credittransfer-inq";
    public static final String BILL_PROMPT_PAY_VALIDATION_GET = "promptpay-billpay-inq";
    public static final String TRANSFER_ERROR_E_WALLET_PREFIX = "ppew_";
}

package com.tmb.oneapp.paymentservice.constant;


import lombok.Getter;

import java.io.Serializable;

@Getter
public enum ResponseCode implements Serializable {

    SUCCESS(PaymentServiceConstant.SUCCESS_CODE, PaymentServiceConstant.SUCCESS_MESSAGE),
    SUCCESS_V2("000000", "Success"),
    DB_FAILED(PaymentServiceConstant.DB_FAILED_CODE, PaymentServiceConstant.FAILED_MESSAGE),
    UNABLE_TO_CONNECT_MONGO_DB_ERROR("106002", "Unable to connect to MongoDB database."),
    NO_CONFIG_MONGO_DB_ERROR("006003", "There is no config in the MongoDB database."),
    FAILED(PaymentServiceConstant.FAILED_CODE, PaymentServiceConstant.FAILED_MESSAGE),
    DATA_NOT_FOUND("0004", "Data not found"),
    MISSING_FIELD("0011", "Missing fields"),
    MANDAT<PERSON>Y_FIELD("0011", "Mandatory Filed Missing"),
    GENERAL_ERROR("0001", "general error"),
    FAILED_V2("100001", "Unknown error"),
    JWE_ENCRYPTION_FAILED("100002", "JWE encryption failed"),
    JWE_DECRYPTION_FAILED("100003", "JWE decryption failed"),
    JWK_KEY_NOT_FOUND("100004", "JWK key not found"),
    JWS_GENERATION_FAILED("100005", "JWS generation failed"),
    JWS_VERIFICATION_FAILED("100006", "JWS verification failed"),
    JWK_INCORRECT_ALGORITHM("100007", "Incorrect algorithm for JWK key"),
    JWK_NOT_CORRECT_FORMAT("100008", "JWK is not in correct format"),
    JWK_SET_NOT_FOUND("100009", "JWK set not found for the given partner"),
    PAYLOAD_DESERIALIZATION_FAILED("100010", "Failed to deserialize payload"),
    INVALID_KEY_CONFIGURATION("100011", "Invalid key configuration for partner"),
    CIRCUIT_BREAKER_ERROR("0014", "Circuit break error"),
    PB_UPDATE_APP("pb_update_app", "To continue using application and transacting safely, please update your ttb touch to the latest version."),
    NOT_FOUND("404", PaymentServiceConstant.FAILED_MESSAGE),
    CIRCUIT_BREAK_ERROR("0014", "Circuit break error");

    private final String code;
    private final String message;
    private final String service = PaymentServiceConstant.PAYMENT_SERVICE;
    private final String description = null;

    ResponseCode(String code, String message) {
        this.code = code;
        this.message = message;
    }
}

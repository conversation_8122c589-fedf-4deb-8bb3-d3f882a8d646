package com.tmb.oneapp.paymentservice.service;

import com.nimbusds.jose.jwk.JWK;
import com.nimbusds.jose.jwk.JWKSet;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.paymentservice.configuration.JwkConfigProperties;
import com.tmb.oneapp.paymentservice.constant.ResponseCode;
import jakarta.annotation.PostConstruct;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class JwkSetProvider {

    private static final TMBLogger<JwkSetProvider> logger = new TMBLogger<>(JwkSetProvider.class);
    private final JwkConfigProperties jwkConfigProperties;
    private final Map<String, JWKSet> jwkSetMap = new ConcurrentHashMap<>();

    public JwkSetProvider(JwkConfigProperties jwkConfigProperties) {
        this.jwkConfigProperties = jwkConfigProperties;
    }

    @PostConstruct
    void init() {
        if (jwkConfigProperties.getSets() == null || jwkConfigProperties.getSets().getClient() == null) {
            logger.warn("No JWK sets configured in properties. JwkSetProvider will be empty.");
            return;
        }

        jwkConfigProperties.getSets().getClient().forEach((partnerName, json) -> {
            try {
                JWKSet jwkSet = JWKSet.parse(json);
                jwkSetMap.put(partnerName, jwkSet);
                logger.info("JWKSet for partner '{}' loaded successfully with {} keys.", partnerName, jwkSet.size());
            } catch (ParseException e) {
                logger.error("Failed to parse JWKSet JSON for partner '{}'", partnerName, e);
                TMBCommonException cause = new TMBCommonException(
                        ResponseCode.JWK_NOT_CORRECT_FORMAT.getCode(),
                        "Cannot parse JWKSet for partner: " + partnerName,
                        ResponseCode.JWK_NOT_CORRECT_FORMAT.getService(),
                        HttpStatus.INTERNAL_SERVER_ERROR, e);
                throw new IllegalStateException(cause.getMessage(), cause);
            }
        });
    }

    /**
     * Get the JWK set for a specific partner.
     *
     * @param partnerName The identifier for the partner.
     * @return The loaded JWKSet for the partner.
     * @throws TMBCommonException if the JWKSet for the partner is not found.
     */
    public JWKSet getJwkSet(String partnerName) throws TMBCommonException {
        JWKSet jwkSet = jwkSetMap.get(partnerName);
        if (jwkSet == null) {
            logger.error("JWKSet for partner '{}' not found.", partnerName);
            throw new TMBCommonException(
                    ResponseCode.JWK_SET_NOT_FOUND.getCode(),
                    "Didn’t provide key for new partner: " + partnerName,
                    ResponseCode.JWK_SET_NOT_FOUND.getService(),
                    HttpStatus.INTERNAL_SERVER_ERROR, null);
        }
        return jwkSet;
    }

    /**
     * Find a specific key by its Key ID (kid) within a specific partner's JWKSet.
     *
     * @param partnerName The partner identifier.
     * @param keyId    The Key ID to search for.
     * @return The JWK corresponding to the given Key ID.
     * @throws TMBCommonException if the key or partner's JWKSet is not found.
     */
    public JWK getKeyById(String partnerName, String keyId) throws TMBCommonException {
        JWKSet jwkSet = getJwkSet(partnerName);
        JWK jwk = jwkSet.getKeyByKeyId(keyId);
        if (jwk == null) {
            logger.error("JWK with kid '{}' not found in JWKSet for partner '{}'.", keyId, partnerName);
            throw new TMBCommonException(
                    ResponseCode.JWK_KEY_NOT_FOUND.getCode(),
                    "Cannot decrypt because Partner sent incorrect key id: " + keyId,
                    ResponseCode.JWK_KEY_NOT_FOUND.getService(),
                    HttpStatus.INTERNAL_SERVER_ERROR, null);
        }
        return jwk;
    }
}

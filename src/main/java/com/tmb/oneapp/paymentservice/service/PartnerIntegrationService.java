package com.tmb.oneapp.paymentservice.service;

import com.nimbusds.jose.jwk.JWK;
import com.nimbusds.jose.jwk.JWKSet;
import com.nimbusds.jose.jwk.KeyUse;
import com.nimbusds.jose.jwk.RSAKey;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.paymentservice.constant.ResponseCode;
import com.tmb.oneapp.paymentservice.utils.JoseUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class PartnerIntegrationService {

    private static final TMBLogger<PartnerIntegrationService> logger = new TMBLogger<>(PartnerIntegrationService.class);

    private final JwkSetProvider jwkSetProvider;

    /**
     * Signs the given payload for a specific partner using their configured private key.
     *
     * @param partnerName The name of the partner (client ID) to sign for.
     * @param payload     The payload to be signed.
     * @return A JWS compact serialization string.
     * @throws TMBCommonException if signing fails or the key configuration is invalid.
     */
    public String signData(String partnerName, Map<String, Object> payload) throws TMBCommonException {
        RSAKey privateKey = getPrivateKeyForSigning(partnerName);
        return JoseUtils.generateJws(privateKey, payload);
    }

    /**
     * Decrypts a JWE string using the partner's private key.
     *
     * @param partnerName The name of the partner (client ID).
     * @param jweString   The JWE string to decrypt.
     * @return The decrypted plaintext.
     * @throws TMBCommonException if decryption fails.
     */
    public String decryptData(String partnerName, String jweString) throws TMBCommonException {
        JWKSet jwkSet = jwkSetProvider.getJwkSet(partnerName);
        return JoseUtils.decrypt(jweString, jwkSet);
    }

    /**
     * Retrieves the specific private RSA key for signing from the partner's JWKSet.
     * <p>
     * This method enforces that there must be exactly one private RSA key in the set
     * to avoid ambiguity and ensure the correct key is used for signing.
     *
     * @param partnerName The name of the partner (client ID).
     * @return The single private {@link RSAKey} for the partner.
     * @throws TMBCommonException if exactly one private RSA key is not found.
     */
    private RSAKey getPrivateKeyForSigning(String partnerName) throws TMBCommonException {
        JWKSet jwkSet = jwkSetProvider.getJwkSet(partnerName);

        List<RSAKey> privateKeys = jwkSet.getKeys().stream()
                .filter(JWK::isPrivate)
                .filter(k -> KeyUse.SIGNATURE.equals(k.getKeyUse()))
                .filter(k -> k instanceof RSAKey)
                .map(k -> (RSAKey) k)
                .toList();

        if (privateKeys.size() != 1) {
            String errorMessage = "Expected exactly one private RSA key for partner '" + partnerName + "', but found " + privateKeys.size();
            logger.error(errorMessage);
            throw new TMBCommonException(
                    ResponseCode.INVALID_KEY_CONFIGURATION.getCode(),
                    errorMessage,
                    ResponseCode.INVALID_KEY_CONFIGURATION.getService(),
                    HttpStatus.INTERNAL_SERVER_ERROR,
                    null);
        }

        return privateKeys.get(0);
    }
}

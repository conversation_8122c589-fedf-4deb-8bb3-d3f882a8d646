package com.tmb.oneapp.paymentservice.utils;

import com.nimbusds.jose.EncryptionMethod;
import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.JWEAlgorithm;
import com.nimbusds.jose.JWEHeader;
import com.nimbusds.jose.JWEObject;
import com.nimbusds.jose.JWSAlgorithm;
import com.nimbusds.jose.JWSHeader;
import com.nimbusds.jose.JWSObject;
import com.nimbusds.jose.Payload;
import com.nimbusds.jose.crypto.RSADecrypter;
import com.nimbusds.jose.crypto.RSAEncrypter;
import com.nimbusds.jose.crypto.RSASSASigner;
import com.nimbusds.jose.crypto.RSASSAVerifier;
import com.nimbusds.jose.jwk.JWK;
import com.nimbusds.jose.jwk.JWKSet;
import com.nimbusds.jose.jwk.RSAKey;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.paymentservice.constant.ResponseCode;
import lombok.experimental.UtilityClass;
import org.springframework.http.HttpStatus;

import java.text.ParseException;
import java.util.Map;

@UtilityClass
public class JoseUtils {

    private static final TMBLogger<JoseUtils> logger = new TMBLogger<>(JoseUtils.class);

    private static final JWSAlgorithm SIGNATURE_ALGORITHM = JWSAlgorithm.RS256;
    private static final JWEAlgorithm KEY_ENCRYPTION_ALGORITHM = JWEAlgorithm.RSA_OAEP_256;
    private static final EncryptionMethod CONTENT_ENCRYPTION_ALGORITHM = EncryptionMethod.A256GCM;

    /**
     * Encrypts a plaintext using JWE with a given RSA public key.
     *
     * @param plainText    The text to encrypt.
     * @param rsaPublicKey The RSA public key to use for encryption.
     * @return The JWE Compact Serialization string.
     * @throws TMBCommonException if encryption fails.
     */
    public String encrypt(String plainText, RSAKey rsaPublicKey) throws TMBCommonException {
        try {
            JWEHeader header = new JWEHeader.Builder(KEY_ENCRYPTION_ALGORITHM, CONTENT_ENCRYPTION_ALGORITHM)
                    .contentType("application/json")
                    .keyID(rsaPublicKey.getKeyID())
                    .build();

            JWEObject jweObject = new JWEObject(header, new Payload(plainText));
            RSAEncrypter encrypter = new RSAEncrypter(rsaPublicKey);
            jweObject.encrypt(encrypter);
            return jweObject.serialize();

        } catch (JOSEException e) {
            logger.error("Error during JWE encryption with JWK", e);
            throw new TMBCommonException(ResponseCode.JWE_ENCRYPTION_FAILED.getCode(), ResponseCode.JWE_ENCRYPTION_FAILED.getMessage(), ResponseCode.JWE_ENCRYPTION_FAILED.getService(), HttpStatus.INTERNAL_SERVER_ERROR, e);
        }
    }

    /**
     * Decrypts a JWE string using the appropriate key from a JWKSet.
     *
     * @param jweString The JWE Compact Serialization string.
     * @param jwkSet    The JWKSet containing the decryption key.
     * @return The decrypted plaintext.
     * @throws TMBCommonException if decryption fails.
     */
    public String decrypt(String jweString, JWKSet jwkSet) throws TMBCommonException {
        try {
            JWEObject jweObject = JWEObject.parse(jweString);

            String keyId = jweObject.getHeader().getKeyID();
            if (keyId == null) {
                logger.error("JWE header does not contain a Key ID (kid)");
                throw new TMBCommonException(ResponseCode.JWE_DECRYPTION_FAILED.getCode(), "Key ID difference between partner and os", ResponseCode.JWE_DECRYPTION_FAILED.getService(), HttpStatus.INTERNAL_SERVER_ERROR, null);
            }

            JWK key = jwkSet.getKeyByKeyId(keyId);
            if (key == null) {
                String errorMessage = "Cannot decrypt because Partner sent incorrect key id " + keyId;
                logger.error("Private key with kid '{}' not found in JWKSet", keyId);
                throw new TMBCommonException(ResponseCode.JWK_KEY_NOT_FOUND.getCode(), errorMessage, ResponseCode.JWK_KEY_NOT_FOUND.getService(), HttpStatus.INTERNAL_SERVER_ERROR, null);
            }
            if (!(key instanceof RSAKey)) {
                logger.error("Key with kid '{}' is not an RSA key", keyId);
                throw new TMBCommonException(ResponseCode.JWK_INCORRECT_ALGORITHM.getCode(), "Cannot decrypt because Incorrect algorithm", ResponseCode.JWK_INCORRECT_ALGORITHM.getService(), HttpStatus.INTERNAL_SERVER_ERROR, null);
            }

            RSADecrypter decrypter = new RSADecrypter(key.toRSAKey());
            jweObject.decrypt(decrypter);

            return jweObject.getPayload().toString();

        } catch (ParseException | JOSEException e) {
            logger.error("Error during JWE decryption with JWKSet", e);
            throw new TMBCommonException(ResponseCode.JWE_DECRYPTION_FAILED.getCode(), "Decryption failed", ResponseCode.JWE_DECRYPTION_FAILED.getService(), HttpStatus.INTERNAL_SERVER_ERROR, e);
        }
    }

    /**
     * Generates a JWS for a given payload using a private RSA key.
     *
     * @param rsaPrivateKey The RSA private key for signing.
     * @param claims        The claims to include in the JWS payload.
     * @return The JWS Compact Serialization string.
     * @throws TMBCommonException if JWS generation fails.
     */
    public String generateJws(RSAKey rsaPrivateKey, Map<String, Object> claims) throws TMBCommonException {
        try {
            JWSHeader header = new JWSHeader.Builder(SIGNATURE_ALGORITHM)
                    .keyID(rsaPrivateKey.getKeyID())
                    .build();

            JWSObject jwsObject = new JWSObject(header, new Payload(claims));
            RSASSASigner signer = new RSASSASigner(rsaPrivateKey);
            jwsObject.sign(signer);
            return jwsObject.serialize();

        } catch (JOSEException e) {
            logger.error("Error during JWS generation with JWK", e);
            throw new TMBCommonException(ResponseCode.JWS_GENERATION_FAILED.getCode(), ResponseCode.JWS_GENERATION_FAILED.getMessage(), ResponseCode.JWS_GENERATION_FAILED.getService(), HttpStatus.INTERNAL_SERVER_ERROR, e);
        }
    }

    /**
     * Verifies a JWS signature using a public RSA key.
     *
     * @param jwsString    The JWS Compact Serialization string.
     * @param rsaPublicKey The RSA public key for verification.
     * @return The payload if the signature is valid.
     * @throws TMBCommonException if verification fails.
     */
    public Payload verifyJws(String jwsString, RSAKey rsaPublicKey) throws TMBCommonException {
        try {
            JWSObject jwsObject = JWSObject.parse(jwsString);
            RSASSAVerifier verifier = new RSASSAVerifier(rsaPublicKey);

            if (jwsObject.verify(verifier)) {
                return jwsObject.getPayload();
            } else {
                throw new JOSEException("JWS signature verification failed.");
            }
        } catch (ParseException | JOSEException e) {
            logger.error("JWS verification failed", e);
            throw new TMBCommonException(ResponseCode.JWS_VERIFICATION_FAILED.getCode(), ResponseCode.JWS_VERIFICATION_FAILED.getMessage(), ResponseCode.JWS_VERIFICATION_FAILED.getService(), HttpStatus.INTERNAL_SERVER_ERROR, e);
        }
    }
}

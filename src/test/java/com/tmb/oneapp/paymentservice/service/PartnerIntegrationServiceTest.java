package com.tmb.oneapp.paymentservice.service;

import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.jwk.JWKSet;
import com.nimbusds.jose.jwk.KeyUse;
import com.nimbusds.jose.jwk.RSAKey;
import com.nimbusds.jose.jwk.gen.RSAKeyGenerator;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.paymentservice.constant.ResponseCode;
import com.tmb.oneapp.paymentservice.utils.JoseUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PartnerIntegrationServiceTest {

    @Mock
    private JwkSetProvider jwkSetProvider;

    @InjectMocks
    private PartnerIntegrationService partnerIntegrationService;

    private RSAKey rsaPrivateKey;
    private RSAKey rsaPublicKey;
    private Map<String, Object> payload;
    private final String partnerName = "test-partner";

    @BeforeEach
    void setUp() throws JOSEException {
        rsaPrivateKey = new RSAKeyGenerator(2048)
                .keyID(UUID.randomUUID().toString())
                .keyUse(KeyUse.SIGNATURE)
                .generate();
        rsaPublicKey = rsaPrivateKey.toPublicJWK();
        payload = new HashMap<>();
        payload.put("sub", "**********");
        payload.put("name", "John Doe");
    }

    @Test
    void testSignDataWhenHappyPathShouldReturnJwsString() throws TMBCommonException {
        JWKSet jwkSet = new JWKSet(rsaPrivateKey);
        when(jwkSetProvider.getJwkSet(partnerName)).thenReturn(jwkSet);

        try (MockedStatic<JoseUtils> joseUtilsMockedStatic = Mockito.mockStatic(JoseUtils.class)) {
            String expectedJws = "signed-jws-string";
            joseUtilsMockedStatic.when(() -> JoseUtils.generateJws(any(RSAKey.class), any(Map.class)))
                    .thenReturn(expectedJws);

            String actualJws = partnerIntegrationService.signData(partnerName, payload);

            assertEquals(expectedJws, actualJws);
            joseUtilsMockedStatic.verify(() -> JoseUtils.generateJws(eq(rsaPrivateKey), eq(payload)));
        }
    }

    @Test
    void testSignDataWhenNoPrivateSigningKeyInJwkSetShouldThrowException() throws TMBCommonException, JOSEException {
        RSAKey encKey = new RSAKeyGenerator(2048).keyUse(KeyUse.ENCRYPTION).generate();
        JWKSet jwkSetWithOnlyEncKey = new JWKSet(encKey);
        when(jwkSetProvider.getJwkSet(partnerName)).thenReturn(jwkSetWithOnlyEncKey);

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> partnerIntegrationService.signData(partnerName, payload));

        assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
        assertEquals("Invalid key configuration for partner: " + partnerName, exception.getErrorMessage());
    }

    @Test
    void testSignDataWhenMultiplePrivateSigningKeysInJwkSetShouldThrowException() throws TMBCommonException, JOSEException {
        RSAKey anotherSigningKey = new RSAKeyGenerator(2048).keyID("another-key").keyUse(KeyUse.SIGNATURE).generate();
        JWKSet jwkSetWithMultipleKeys = new JWKSet(Arrays.asList(rsaPrivateKey, anotherSigningKey));
        when(jwkSetProvider.getJwkSet(partnerName)).thenReturn(jwkSetWithMultipleKeys);

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> partnerIntegrationService.signData(partnerName, payload));

        assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
        assertEquals("Invalid key configuration for partner: " + partnerName, exception.getErrorMessage());
    }

    @Test
    void testSignDataWhenJwkSetHasBothSignAndEncKeysShouldPickSignKey() throws TMBCommonException, JOSEException {
        RSAKey encKey = new RSAKeyGenerator(2048).keyID("enc-key").keyUse(KeyUse.ENCRYPTION).generate();
        JWKSet jwkSetWithMixedKeys = new JWKSet(Arrays.asList(rsaPrivateKey, encKey));
        when(jwkSetProvider.getJwkSet(partnerName)).thenReturn(jwkSetWithMixedKeys);

        try (MockedStatic<JoseUtils> joseUtilsMockedStatic = Mockito.mockStatic(JoseUtils.class)) {
            String expectedJws = "signed-jws-string";
            joseUtilsMockedStatic.when(() -> JoseUtils.generateJws(any(RSAKey.class), any(Map.class)))
                    .thenReturn(expectedJws);

            String actualJws = partnerIntegrationService.signData(partnerName, payload);

            assertEquals(expectedJws, actualJws);
            joseUtilsMockedStatic.verify(() -> JoseUtils.generateJws(eq(rsaPrivateKey), eq(payload)));
        }
    }

    @Test
    void testSignDataWhenJwkSetIsEmptyShouldThrowException() throws TMBCommonException {
        JWKSet emptyJwkSet = new JWKSet(Collections.emptyList());
        when(jwkSetProvider.getJwkSet(partnerName)).thenReturn(emptyJwkSet);

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> partnerIntegrationService.signData(partnerName, payload));

        assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
        assertEquals("Invalid key configuration for partner: " + partnerName, exception.getErrorMessage());
    }

    @Test
    void testSignDataWhenPartnerNotFoundShouldThrowException() throws TMBCommonException {
        TMBCommonException expectedException = new TMBCommonException(
                ResponseCode.FAILED_V2.getCode(),
                "JWKSet not found for client: " + partnerName,
                ResponseCode.FAILED.getService(),
                HttpStatus.OK, null);
        when(jwkSetProvider.getJwkSet(partnerName)).thenThrow(expectedException);

        TMBCommonException actualException = assertThrows(TMBCommonException.class,
                () -> partnerIntegrationService.signData(partnerName, payload));

        assertEquals(expectedException, actualException);
    }

    @Test
    void testDecryptDataWhenSuccessShouldReturnDecryptedString() throws TMBCommonException {
        String jweString = "encrypted.string";
        String expectedDecryptedString = "decrypted-transaction-id";
        JWKSet jwkSet = new JWKSet(rsaPrivateKey);
        when(jwkSetProvider.getJwkSet(partnerName)).thenReturn(jwkSet);

        try (MockedStatic<JoseUtils> joseUtilsMockedStatic = Mockito.mockStatic(JoseUtils.class)) {
            joseUtilsMockedStatic.when(() -> JoseUtils.decrypt(jweString, jwkSet))
                    .thenReturn(expectedDecryptedString);

            String actualDecryptedString = partnerIntegrationService.decryptData(partnerName, jweString);

            assertEquals(expectedDecryptedString, actualDecryptedString);
            joseUtilsMockedStatic.verify(() -> JoseUtils.decrypt(eq(jweString), eq(jwkSet)));
        }
    }

    @Test
    void testDecryptDataWhenProviderThrowsExceptionShouldPropagate() throws TMBCommonException {
        String jweString = "encrypted.string";
        TMBCommonException expectedException = new TMBCommonException(
                ResponseCode.FAILED_V2.getCode(),
                "JWKSet not found for client: " + partnerName,
                ResponseCode.FAILED.getService(),
                HttpStatus.OK, null);
        when(jwkSetProvider.getJwkSet(partnerName)).thenThrow(expectedException);

        TMBCommonException actualException = assertThrows(TMBCommonException.class,
                () -> partnerIntegrationService.decryptData(partnerName, jweString));

        assertEquals(expectedException, actualException);
    }
}

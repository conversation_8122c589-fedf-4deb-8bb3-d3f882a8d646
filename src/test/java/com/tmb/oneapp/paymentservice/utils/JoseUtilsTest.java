package com.tmb.oneapp.paymentservice.utils;

import com.nimbusds.jose.EncryptionMethod;
import com.nimbusds.jose.JWEAlgorithm;
import com.nimbusds.jose.JWEHeader;
import com.nimbusds.jose.JWEObject;
import com.nimbusds.jose.Payload;
import com.nimbusds.jose.crypto.RSAEncrypter;
import com.nimbusds.jose.jwk.Curve;
import com.nimbusds.jose.jwk.ECKey;
import com.nimbusds.jose.jwk.JWKSet;
import com.nimbusds.jose.jwk.RSAKey;
import com.nimbusds.jose.jwk.gen.ECKeyGenerator;
import com.nimbusds.jose.jwk.gen.RSAKeyGenerator;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.paymentservice.constant.ResponseCode;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(MockitoExtension.class)
class JoseUtilsTest {

    private static RSAKey rsaKey;
    private static RSAKey anotherRsaKey;
    private static JWKSet jwkSet;

    @BeforeAll
    static void setUp() throws Exception {
        rsaKey = new RSAKeyGenerator(2048).keyID("test-kid-1").generate();
        anotherRsaKey = new RSAKeyGenerator(2048).keyID("test-kid-2").generate();
        jwkSet = new JWKSet(rsaKey);
    }

    @Test
    void testEncryptAndDecrypt_Success() throws TMBCommonException {
        // Given
        String originalText = "This is a secret message for testing JWE!";
        RSAKey rsaPublicKey = rsaKey.toPublicJWK();

        // When
        String encryptedText = JoseUtils.encrypt(originalText, rsaPublicKey);
        String decryptedText = JoseUtils.decrypt(encryptedText, jwkSet);

        // Then
        assertEquals(originalText, decryptedText);
    }

    @Test
    void testDecrypt_withWrongKey_shouldThrowJwkKeyNotFoundException() throws TMBCommonException {
        // Given
        String encryptedText = JoseUtils.encrypt("test", rsaKey.toPublicJWK());
        JWKSet wrongJwkSet = new JWKSet(anotherRsaKey);

        // When
        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> JoseUtils.decrypt(encryptedText, wrongJwkSet));

        // Then
        assertEquals(ResponseCode.JWK_KEY_NOT_FOUND.getCode(), exception.getErrorCode());
        assertTrue(exception.getErrorMessage().contains("Partner sent incorrect key id"));
    }

    @Test
    void testDecrypt_withNoKidInHeader_shouldThrowJweDecryptionFailedException() throws Exception {
        // Given
        JWEHeader header = new JWEHeader(JWEAlgorithm.RSA_OAEP_256, EncryptionMethod.A256GCM);
        JWEObject jweObject = new JWEObject(header, new Payload("test"));
        jweObject.encrypt(new RSAEncrypter(rsaKey.toPublicJWK()));
        String jweString = jweObject.serialize();

        // When
        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> JoseUtils.decrypt(jweString, jwkSet));

        // Then
        assertEquals(ResponseCode.JWE_DECRYPTION_FAILED.getCode(), exception.getErrorCode());
        assertEquals("Key ID difference between partner and os", exception.getErrorMessage());
    }

    @Test
    void testDecrypt_withMalformedJwe_shouldThrowJweDecryptionFailedException() {
        // Given
        String malformedJwe = "this.is.not.a.valid.jwe";

        // When
        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> JoseUtils.decrypt(malformedJwe, jwkSet));

        // Then
        assertEquals(ResponseCode.JWE_DECRYPTION_FAILED.getCode(), exception.getErrorCode());
        assertEquals("Decryption failed", exception.getErrorMessage());
    }

    @Test
    void testDecrypt_withNonRsaKeyInSet_shouldThrowJwkIncorrectAlgorithmException() throws Exception {
        // Given
        ECKey ecKey = new ECKeyGenerator(Curve.P_256)
                .keyID(rsaKey.getKeyID())
                .generate();
        JWKSet mixedJwkSet = new JWKSet(ecKey);
        String encryptedText = JoseUtils.encrypt("test", rsaKey.toPublicJWK());

        // When
        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> JoseUtils.decrypt(encryptedText, mixedJwkSet));

        // Then
        assertEquals(ResponseCode.JWK_INCORRECT_ALGORITHM.getCode(), exception.getErrorCode());
        assertEquals("Cannot decrypt because Incorrect algorithm", exception.getErrorMessage());
    }

    @Test
    void testGenerateAndVerifyJws_Success() throws TMBCommonException {
        // Given
        Map<String, Object> claims = Collections.singletonMap("sub", "1234567890");

        // When
        String jwsString = JoseUtils.generateJws(rsaKey, claims);
        Payload verifiedPayload = JoseUtils.verifyJws(jwsString, rsaKey.toPublicJWK());

        // Then
        assertEquals("{\"sub\":\"1234567890\"}", verifiedPayload.toString());
    }

    @Test
    void testVerifyJws_withWrongKey_shouldThrowJwsVerificationFailedException() throws TMBCommonException {
        // Given
        String jwsString = JoseUtils.generateJws(rsaKey, Collections.singletonMap("sub", "user-id"));

        // When
        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> JoseUtils.verifyJws(jwsString, anotherRsaKey.toPublicJWK()));

        // Then
        assertEquals(ResponseCode.JWS_VERIFICATION_FAILED.getCode(), exception.getErrorCode());
    }

    @Test
    void testVerifyJws_withMalformedJws_shouldThrowJwsVerificationFailedException() {
        // Given
        String malformedJws = "this.is.not.a.valid.jws";

        // When
        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> JoseUtils.verifyJws(malformedJws, rsaKey.toPublicJWK()));

        // Then
        assertEquals(ResponseCode.JWS_VERIFICATION_FAILED.getCode(), exception.getErrorCode());
    }
}
